# -*- coding: utf-8 -*-
"""
Script automático pyRevit para processar família RFA
Criado em nome de Jesus
Versão simplificada e funcional
"""

# Importações necessárias
from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from System.Collections.Generic import List
import os

def main():
    """
    Função principal - Em nome de Jesus
    """
    print("=== PROCESSAMENTO AUTOMÁTICO DE FAMÍLIA ===")
    print("Em nome de Jesus")
    print("=" * 50)
    
    # Obter contexto atual
    app = __revit__.Application
    uidoc = __revit__.ActiveUIDocument
    current_doc = uidoc.Document if uidoc else None
    
    try:
        # 1. Criar novo documento
        print("1. Criando novo documento...")
        new_doc = app.NewProjectDocument(UnitSystem.Metric)
        print("   Novo documento criado!")
        
        # 2. Iniciar transação
        with Transaction(new_doc, "Processamento Automático - Em nome de Jesus") as trans:
            trans.Start()
            
            try:
                # 3. <PERSON><PERSON>r piso 5x5
                print("2. Criando piso 5x5 metros...")
                floor_id = create_floor_5x5(new_doc)
                print("   Piso criado com ID: {}".format(floor_id))
                
                # 4. Carregar família
                print("3. Carregando família...")
                script_dir = os.path.dirname(__file__)
                family_path = os.path.join(script_dir, "temp", "teste.rfa")
                
                if not os.path.exists(family_path):
                    print("   ERRO: Arquivo não encontrado: {}".format(family_path))
                    trans.RollBack()
                    return False
                
                family_symbol = load_and_activate_family(new_doc, family_path)
                if not family_symbol:
                    print("   ERRO: Falha ao carregar família")
                    trans.RollBack()
                    return False
                
                print("   Família carregada: {}".format(family_symbol.Family.Name))
                
                # 5. Inserir família no piso
                print("4. Inserindo família no piso...")
                family_instance = insert_family_on_floor(new_doc, family_symbol, floor_id)
                if not family_instance:
                    print("   ERRO: Falha ao inserir família")
                    trans.RollBack()
                    return False
                
                print("   Família inserida com sucesso!")
                
                # 6. Remover piso
                print("5. Removendo piso...")
                new_doc.Delete(floor_id)
                print("   Piso removido!")
                
                # Confirmar transação
                trans.Commit()
                print("   Transação confirmada!")
                
            except Exception as e:
                print("   ERRO durante processamento: {}".format(str(e)))
                trans.RollBack()
                return False
        
        # 7. Salvar documento
        print("6. Salvando documento...")
        save_path = os.path.join(script_dir, "temp", "modelo_processado_auto.rvt")
        
        save_options = SaveAsOptions()
        save_options.OverwriteExistingFile = True
        new_doc.SaveAs(save_path, save_options)
        
        print("   Documento salvo: {}".format(save_path))
        print("=" * 50)
        print("PROCESSAMENTO CONCLUÍDO COM SUCESSO!")
        print("Em nome de Jesus")
        return True
        
    except Exception as e:
        print("ERRO GERAL: {}".format(str(e)))
        return False

def create_floor_5x5(doc):
    """
    Criar piso 5x5 metros na origem
    """
    # Converter 5 metros para pés (unidade interna do Revit)
    size_feet = UnitUtils.ConvertToInternalUnits(5.0, UnitTypeId.Meters)
    half_size = size_feet / 2.0
    
    # Criar pontos do retângulo
    points = [
        XYZ(-half_size, -half_size, 0),
        XYZ(half_size, -half_size, 0),
        XYZ(half_size, half_size, 0),
        XYZ(-half_size, half_size, 0)
    ]
    
    # Criar curvas
    curves = CurveArray()
    for i in range(len(points)):
        start_point = points[i]
        end_point = points[(i + 1) % len(points)]
        line = Line.CreateBound(start_point, end_point)
        curves.Append(line)
    
    # Obter tipo de piso padrão
    floor_types = FilteredElementCollector(doc).OfClass(FloorType).ToElements()
    floor_type = floor_types[0] if floor_types else None
    
    if not floor_type:
        raise Exception("Nenhum tipo de piso encontrado")
    
    # Criar piso
    floor = doc.Create.NewFloor(curves, floor_type, doc.ActiveView.GenLevel, False)
    return floor.Id

def load_and_activate_family(doc, family_path):
    """
    Carregar e ativar família
    """
    # Carregar família
    family_loaded = doc.LoadFamily(family_path)
    
    if not family_loaded:
        return None
    
    # Encontrar símbolo da família
    collector = FilteredElementCollector(doc).OfClass(FamilySymbol)
    family_name = os.path.splitext(os.path.basename(family_path))[0]
    
    for symbol in collector:
        if family_name.lower() in symbol.Family.Name.lower():
            if not symbol.IsActive:
                symbol.Activate()
                doc.Regenerate()
            return symbol
    
    return None

def insert_family_on_floor(doc, family_symbol, floor_id):
    """
    Inserir família na face superior do piso
    """
    # Obter elemento do piso
    floor_element = doc.GetElement(floor_id)
    
    # Obter geometria do piso
    options = Options()
    options.ComputeReferences = True
    geom_element = floor_element.get_Geometry(options)
    
    # Encontrar face superior
    top_face = None
    for geom_obj in geom_element:
        if isinstance(geom_obj, Solid):
            for face in geom_obj.Faces:
                if isinstance(face, PlanarFace):
                    normal = face.FaceNormal
                    if normal.Z > 0.9:  # Face apontando para cima
                        top_face = face
                        break
            if top_face:
                break
    
    if not top_face:
        raise Exception("Face superior do piso não encontrada")
    
    # Obter centro da face
    bbox = top_face.GetBoundingBox()
    center_uv = UV((bbox.Min.U + bbox.Max.U) / 2.0, (bbox.Min.V + bbox.Max.V) / 2.0)
    center_point = top_face.Evaluate(center_uv)
    
    # Inserir família
    family_instance = doc.Create.NewFamilyInstance(
        center_point,
        family_symbol,
        top_face,
        doc.ActiveView.GenLevel,
        StructuralType.NonStructural
    )
    
    return family_instance

# Executar script
if __name__ == "__main__":
    main()
