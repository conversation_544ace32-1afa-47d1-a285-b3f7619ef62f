# Script Automático pyRevit - Processamento de Família
## Em nome de Jesus

### Descrição
Este script automático em pyRevit realiza o seguinte processo:

1. **Cria um novo modelo** do Revit
2. **Insere um piso 5x5 metros** centralizado na origem
3. **Carrega e insere a família** fornecida na face superior do piso
4. **Remove o piso**, deixando apenas a família
5. **Salva o modelo** automaticamente

### Arquivos Criados

- `pyrevit_family_auto.py` - Script principal otimizado
- `executar_familia.py` - Script de execução simples
- `pyrevit_auto_family_processor.py` - V<PERSON><PERSON> completa (backup)
- `run_family_processor.py` - Executador alternativo
- `execute_family_processor.bat` - Executador batch

### Como Usar

#### Método 1: Console pyRevit (Recomendado)
1. Abra o Revit
2. Abra o console pyRevit (pyRevit > Developer > Console)
3. Execute o comando:
```python
exec(open(r'c:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\pyrevit_family_auto.py').read())
```

#### Método 2: Script Executável
1. Abra o Revit
2. Abra o console pyRevit
3. Execute:
```python
exec(open(r'executar_familia.py').read())
```

#### Método 3: Batch File
1. Execute o arquivo `execute_family_processor.bat`

### Requisitos

- **Revit 2024** instalado em: `C:\Program Files\Autodesk\Revit 2024`
- **pyRevit** instalado em: `C:\Users\<USER>\AppData\Roaming\pyRevit-Master`
- **Arquivo de família** `teste.rfa` na pasta `temp/`

### Arquivo de Teste

O script usa o arquivo: `temp/teste.rfa`

### Saída

O modelo processado será salvo como: `temp/modelo_processado_auto.rvt`

### Funcionalidades

✅ **Totalmente automático** - sem intervenção manual
✅ **Criação de novo modelo** 
✅ **Inserção de piso temporário 5x5m**
✅ **Carregamento automático de família**
✅ **Inserção na face superior do piso**
✅ **Remoção do piso auxiliar**
✅ **Salvamento automático**
✅ **Tratamento de erros completo**
✅ **Logs detalhados do processo**

### Logs de Execução

O script fornece logs detalhados:
```
=== PROCESSAMENTO AUTOMÁTICO DE FAMÍLIA ===
Em nome de Jesus
==================================================
1. Criando novo documento...
   Novo documento criado!
2. Criando piso 5x5 metros...
   Piso criado com ID: 12345
3. Carregando família...
   Família carregada: teste
4. Inserindo família no piso...
   Família inserida com sucesso!
5. Removendo piso...
   Piso removido!
   Transação confirmada!
6. Salvando documento...
   Documento salvo: temp/modelo_processado_auto.rvt
==================================================
PROCESSAMENTO CONCLUÍDO COM SUCESSO!
Em nome de Jesus
```

### Solução de Problemas

**Erro: Arquivo não encontrado**
- Verifique se `temp/teste.rfa` existe

**Erro: Contexto pyRevit**
- Execute dentro do console pyRevit, não Python externo

**Erro: Permissões**
- Execute o Revit como administrador

**Erro: Tipo de piso**
- Certifique-se que há tipos de piso no template

### Criado em nome de Jesus
Este script foi desenvolvido para automatizar completamente o processamento de famílias RFA no Revit usando pyRevit.
