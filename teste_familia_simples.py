# -*- coding: utf-8 -*-
"""
Script SIMPLES para testar processamento de família
Em nome de Jesus
"""

print("=== TESTE SIMPLES DE FAMÍLIA ===")
print("Em nome de Jesus")

# Importações básicas
from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
import os

# Obter contexto
app = __revit__.Application
uidoc = __revit__.ActiveUIDocument

print("Contexto obtido com sucesso!")

# Criar novo documento
print("Criando novo documento...")
new_doc = app.NewProjectDocument(UnitSystem.Metric)
print("Documento criado!")

# Iniciar transação
print("Iniciando transação...")
with Transaction(new_doc, "Teste Família - Em nome de Jesus") as trans:
    trans.Start()
    
    try:
        # Carregar família
        print("Carregando família...")
        family_path = r"c:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\teste.rfa"
        
        if os.path.exists(family_path):
            print("Arquivo encontrado: " + family_path)
            
            # Carregar família
            family_loaded = new_doc.LoadFamily(family_path)
            
            if family_loaded:
                print("Família carregada com sucesso!")
                
                # Encontrar símbolo da família
                collector = FilteredElementCollector(new_doc).OfClass(FamilySymbol)
                family_symbol = None
                
                for symbol in collector:
                    if "teste" in symbol.Family.Name.lower():
                        family_symbol = symbol
                        break
                
                if family_symbol:
                    print("Símbolo encontrado: " + family_symbol.Family.Name)
                    
                    # Ativar símbolo
                    if not family_symbol.IsActive:
                        family_symbol.Activate()
                        new_doc.Regenerate()
                    
                    # Inserir família na origem
                    print("Inserindo família na origem...")
                    origin = XYZ(0, 0, 0)
                    
                    family_instance = new_doc.Create.NewFamilyInstance(
                        origin,
                        family_symbol,
                        StructuralType.NonStructural
                    )
                    
                    if family_instance:
                        print("Família inserida com sucesso!")
                    else:
                        print("ERRO: Falha ao inserir família")
                else:
                    print("ERRO: Símbolo da família não encontrado")
            else:
                print("ERRO: Falha ao carregar família")
        else:
            print("ERRO: Arquivo não encontrado: " + family_path)
        
        # Confirmar transação
        trans.Commit()
        print("Transação confirmada!")
        
    except Exception as e:
        print("ERRO: " + str(e))
        trans.RollBack()

# Salvar documento
print("Salvando documento...")
save_path = r"c:\Users\<USER>\OneDrive\old\Documentos\bimex-object-market\bimex-object-market\temp\teste_simples.rvt"

save_options = SaveAsOptions()
save_options.OverwriteExistingFile = True

new_doc.SaveAs(save_path, save_options)
print("Documento salvo: " + save_path)

print("=== TESTE CONCLUÍDO ===")
print("Em nome de Jesus")
