# -*- coding: utf-8 -*-
"""
Script automático pyRevit para processar família RFA
Criado em nome de Jesus
"""

import clr
import sys
import os

# Adicionar referências do Revit
clr.AddReference('RevitAPI')
clr.AddReference('RevitAPIUI')
clr.AddReference('System')

from Autodesk.Revit.DB import *
from Autodesk.Revit.UI import *
from Autodesk.Revit.ApplicationServices import *
from System.Collections.Generic import List
from System import Guid
import System

# Obter contexto do pyRevit
print("=== Iniciando Script Automático de Família ===")
print("Em nome de Jesus")

# Acessar variáveis globais do pyRevit
app = __revit__.Application
uidoc = __revit__.ActiveUIDocument
doc = uidoc.Document if uidoc else None

print("Contexto pyRevit carregado com sucesso!")

def create_automatic_family_processor():
    """
    Função principal para processar família automaticamente
    Em nome de Jesus
    """

    # Verificar se temos acesso ao contexto pyRevit
    if app is None:
        print("ERRO: Não foi possível acessar o contexto do Revit")
        print("Este script deve ser executado dentro do pyRevit")
        return False

    try:
        # 1. Criar novo documento baseado em template padrão
        print("Criando novo documento...")

        # Usar template padrão do Revit
        template_path = None  # Usar template padrão
        new_doc = app.NewProjectDocument(template_path)

        # Ativar o novo documento
        new_uidoc = UIDocument(new_doc)

        print("Novo documento criado com sucesso!")

        # 2. Criar transação para todas as operações
        with Transaction(new_doc, "Processamento Automático de Família - Em nome de Jesus") as trans:
            trans.Start()

            try:
                # 3. Criar piso 5x5 centralizado na origem
                print("Criando piso 5x5...")
                floor_id = create_floor_5x5(new_doc)

                # 4. Carregar e inserir família
                print("Carregando família...")
                family_path = os.path.join(os.path.dirname(__file__), "temp", "teste.rfa")
                family_symbol = load_family(new_doc, family_path)

                if family_symbol:
                    print("Inserindo família no piso...")
                    family_instance = insert_family_on_floor(new_doc, family_symbol, floor_id)

                    # 5. Apagar o piso
                    print("Removendo piso...")
                    new_doc.Delete(floor_id)

                    print("Piso removido com sucesso!")
                else:
                    print("Erro: Não foi possível carregar a família")
                    trans.RollBack()
                    return False

                # Confirmar transação
                trans.Commit()
                print("Transação confirmada com sucesso!")

            except Exception as e:
                print("Erro durante processamento: {}".format(str(e)))
                trans.RollBack()
                return False

        # 6. Salvar o documento
        print("Salvando documento...")
        save_path = os.path.join(os.path.dirname(__file__), "temp", "modelo_processado.rvt")

        # Configurar opções de salvamento
        save_options = SaveAsOptions()
        save_options.OverwriteExistingFile = True

        new_doc.SaveAs(save_path, save_options)
        print("Documento salvo em: {}".format(save_path))

        print("Processamento automático concluído com sucesso em nome de Jesus!")
        return True

    except Exception as e:
        print("Erro geral: {}".format(str(e)))
        return False

def create_floor_5x5(doc):
    """
    Criar piso 5x5 metros centralizado na origem
    """
    try:
        # Converter metros para pés (unidade interna do Revit)
        size_feet = UnitUtils.ConvertToInternalUnits(5.0, DisplayUnitType.DUT_METERS)
        half_size = size_feet / 2.0

        # Criar pontos do retângulo 5x5 centralizado na origem
        points = List[XYZ]()
        points.Add(XYZ(-half_size, -half_size, 0))
        points.Add(XYZ(half_size, -half_size, 0))
        points.Add(XYZ(half_size, half_size, 0))
        points.Add(XYZ(-half_size, half_size, 0))

        # Criar curva fechada
        curves = List[Curve]()
        for i in range(len(points)):
            start_point = points[i]
            end_point = points[(i + 1) % len(points)]
            line = Line.CreateBound(start_point, end_point)
            curves.Add(line)

        # Criar CurveArray
        curve_array = CurveArray()
        for curve in curves:
            curve_array.Append(curve)

        # Obter tipo de piso padrão
        floor_types = FilteredElementCollector(doc).OfClass(FloorType).ToElements()
        floor_type = floor_types[0] if floor_types else None

        if not floor_type:
            raise Exception("Nenhum tipo de piso encontrado")

        # Criar piso
        floor = doc.Create.NewFloor(curve_array, floor_type, doc.ActiveView.GenLevel, False)

        if floor:
            print("Piso 5x5 criado com sucesso!")
            return floor.Id
        else:
            raise Exception("Falha ao criar piso")

    except Exception as e:
        print("Erro ao criar piso: {}".format(str(e)))
        raise

def load_family(doc, family_path):
    """
    Carregar família do arquivo RFA
    """
    try:
        if not os.path.exists(family_path):
            print("Arquivo de família não encontrado: {}".format(family_path))
            return None

        # Carregar família
        family_loaded = doc.LoadFamily(family_path)

        if family_loaded:
            # Obter símbolo da família carregada
            collector = FilteredElementCollector(doc).OfClass(FamilySymbol)

            # Procurar pela família recém-carregada
            for symbol in collector:
                if symbol.Family.Name in os.path.basename(family_path):
                    # Ativar símbolo se necessário
                    if not symbol.IsActive:
                        symbol.Activate()
                        doc.Regenerate()

                    print("Família carregada: {}".format(symbol.Family.Name))
                    return symbol

        print("Erro ao carregar família")
        return None

    except Exception as e:
        print("Erro ao carregar família: {}".format(str(e)))
        return None

def insert_family_on_floor(doc, family_symbol, floor_id):
    """
    Inserir família na face do piso
    """
    try:
        # Obter elemento do piso
        floor_element = doc.GetElement(floor_id)

        if not floor_element:
            raise Exception("Piso não encontrado")

        # Obter geometria do piso
        options = Options()
        options.ComputeReferences = True
        options.DetailLevel = ViewDetailLevel.Fine

        geom_element = floor_element.get_Geometry(options)

        # Encontrar face superior do piso
        top_face = None
        for geom_obj in geom_element:
            if isinstance(geom_obj, Solid):
                for face in geom_obj.Faces:
                    if isinstance(face, PlanarFace):
                        # Verificar se é face superior (normal apontando para cima)
                        normal = face.FaceNormal
                        if normal.Z > 0.9:  # Aproximadamente vertical para cima
                            top_face = face
                            break
                if top_face:
                    break

        if not top_face:
            raise Exception("Face superior do piso não encontrada")

        # Obter centro da face
        bbox = top_face.GetBoundingBox()
        center_uv = UV((bbox.Min.U + bbox.Max.U) / 2.0, (bbox.Min.V + bbox.Max.V) / 2.0)
        center_point = top_face.Evaluate(center_uv)

        # Inserir família no centro da face superior
        family_instance = doc.Create.NewFamilyInstance(
            center_point,
            family_symbol,
            top_face,
            doc.ActiveView.GenLevel,
            StructuralType.NonStructural
        )

        if family_instance:
            print("Família inserida com sucesso na posição: ({}, {}, {})".format(
                center_point.X, center_point.Y, center_point.Z))
            return family_instance
        else:
            raise Exception("Falha ao inserir família")

    except Exception as e:
        print("Erro ao inserir família: {}".format(str(e)))
        raise

# Executar processamento automático
if __name__ == "__main__":
    print("=== Iniciando Processamento Automático de Família ===")
    print("Em nome de Jesus")
    print("=" * 50)

    success = create_automatic_family_processor()

    if success:
        print("=" * 50)
        print("PROCESSAMENTO CONCLUÍDO COM SUCESSO!")
        print("Em nome de Jesus")
    else:
        print("=" * 50)
        print("ERRO NO PROCESSAMENTO!")
        print("Verifique os logs acima para detalhes")
